import pandas as pd
import re

def process_path_separators(input_file, output_file):
    """
    处理CSV文件中的路径分隔符，将箭头符号替换为逗号

    参数:
    input_file: 输入文件路径
    output_file: 输出文件路径
    """
    # 读取CSV文件
    df = pd.read_csv(input_file)

    print("原始数据:")
    print(df.head())
    print("\n" + "="*50 + "\n")

    # 处理path列中的分隔符
    if 'path' in df.columns:
        # 将箭头符号替换为逗号，并清理多余的空格
        df['path'] = df['path'].astype(str).apply(lambda x: re.sub(r'\s*→\s*', ',', x))

        # 清理路径字符串中的多余空格
        df['path'] = df['path'].apply(lambda x: re.sub(r'\s+', '', x))

        print("处理后的数据:")
        print(df.head())
        print("\n" + "="*50 + "\n")

        # 显示处理前后的对比
        print("路径处理对比:")
        for i in range(min(5, len(df))):
            original = pd.read_csv(input_file).iloc[i]['path']
            processed = df.iloc[i]['path']
            print(f"原始: {original}")
            print(f"处理后: {processed}")
            print("-" * 30)

    # 保存处理后的数据
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n处理完成！结果已保存到: {output_file}")

    return df

# 处理文件
if __name__ == "__main__":
    input_file = "resources/最优路径方案.csv"
    output_file = "resources/最优路径方案_processed.csv"

    processed_df = process_path_separators(input_file, output_file)
