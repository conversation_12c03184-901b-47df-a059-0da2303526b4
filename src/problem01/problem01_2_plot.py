import numpy as np
import pandas as pd

from src.__init__ import *

# 读取上传的 Excel 文件
file_path = '../../resources/raw_data/附件1.xlsx'
data = pd.read_excel(file_path)

# 清洗数据，提取有效信息
data_clean: pd.DataFrame = data.iloc[:, [0, 1, 2, 3]].reset_index(drop=True)
data_clean.columns = ['编号', 'x', 'y', '垃圾量']

# 将x, y坐标转换为数值型数据，垃圾量也转换为数值
data_clean['x'] = pd.to_numeric(data_clean['x'], errors='coerce')
data_clean['y'] = pd.to_numeric(data_clean['y'], errors='coerce')
data_clean['垃圾量'] = pd.to_numeric(data_clean['垃圾量'], errors='coerce')

# 提取收集点数据
coordinates = data_clean[['x', 'y']].values
coordinates = np.insert(coordinates, 0, np.array([0, 0]), axis=0)
demands = data_clean['垃圾量'].values
num_locations = len(coordinates)

# 读取最优路径方案数据
result_df = pd.read_csv('../../resources/最优路径方案.csv')

print("最优路径方案数据:")
print(result_df.head())
print(f"\n总共有 {len(result_df)} 辆车")


def parse_path_string(path_str):
    """
    解析路径字符串，返回路径点列表

    参数:
    path_str: 路径字符串，如 "0,13,6,0"

    返回:
    路径点列表，如 [0, 13, 6, 0]
    """
    # 移除引号并分割
    path_str = str(path_str).strip('"').strip("'")
    return [int(x.strip()) for x in path_str.split(',')]


def calculate_route_load(path_points, demands):
    """
    计算路径的装载量

    参数:
    path_points: 路径点列表
    demands: 需求量数组

    返回:
    路径装载量
    """
    load = 0
    # 排除起点和终点（都是0），只计算中间收集点的垃圾量
    for point in path_points[1:-1]:
        if point > 0:  # 确保不是垃圾处理厂
            load += demands[point - 1]  # demands数组索引比点编号小1
    return load


def plot_vehicle_load_distribution(result_df, demands):
    """
    绘制每辆车的运输量分布图

    参数:
    result_df: 包含路径信息的DataFrame
    demands: 需求量数组
    """
    # 解析路径并计算装载量
    vehicle_loads = []
    vehicle_ids = []

    for index, row in result_df.iterrows():
        vehicle_id = row['id']
        path_str = row['path']
        weight = row['w']  # CSV中的w列就是装载量

        # 也可以通过路径计算验证
        path_points = parse_path_string(path_str)
        calculated_load = calculate_route_load(path_points, demands)

        vehicle_ids.append(vehicle_id)
        vehicle_loads.append(weight)  # 使用CSV中的装载量数据

    # 创建柱状图
    plt.figure(figsize=(12, 8))
    bars = plt.bar(vehicle_ids, vehicle_loads, color='skyblue', alpha=0.8, edgecolor='navy', linewidth=1)

    # 在每个柱状图上标注运输量数值
    for i, (bar, load) in enumerate(zip(bars, vehicle_loads)):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{load:.1f}t',
                ha='center', va='bottom', fontsize=10, fontweight='bold')

    # 设置图表属性
    plt.xlabel('车辆编号', fontsize=12)
    plt.ylabel('运输量 (吨)', fontsize=12)
    plt.title('每辆车的运输量分布', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3, axis='y')

    # 设置x轴刻度
    plt.xticks(vehicle_ids)

    # 调整y轴范围，为标注留出空间
    plt.ylim(0, max(vehicle_loads) * 1.15)

    # 添加统计信息
    total_load = sum(vehicle_loads)
    avg_load = total_load / len(vehicle_loads)
    max_load = max(vehicle_loads)
    min_load = min(vehicle_loads)

    # 在图上添加统计信息文本框
    stats_text = f'总运输量: {total_load:.1f}t\n平均运输量: {avg_load:.1f}t\n最大运输量: {max_load:.1f}t\n最小运输量: {min_load:.1f}t'
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
             fontsize=10)

    plt.tight_layout()
    plt.show()

    return vehicle_loads


# 绘制运输量分布图
vehicle_loads = plot_vehicle_load_distribution(result_df, demands)
