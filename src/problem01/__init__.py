import seaborn as sns
from matplotlib import pyplot as plt


#---------------- 解决中文显示问题 -----------------------#

# 设置 seaborn 样式
sns.set_style('whitegrid')

# 解决中文显示问题 - 在设置 seaborn 样式后再设置中文字体
import matplotlib
from matplotlib.font_manager import FontProperties  # 导入 FontProperties

# 设置全局字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong',
                                          'Arial Unicode MS']  # 尝试多种中文字体
matplotlib.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 创建中文字体对象
try:
    chinese_font = FontProperties(fname=r'C:\Windows\Fonts\simhei.ttf')  # Windows 系统黑体字体路径
except:
    try:
        chinese_font = FontProperties(fname=r'C:\Windows\Fonts\msyh.ttc')  # Windows 系统微软雅黑字体路径
    except:
        chinese_font = FontProperties(family='SimHei')  # 使用字体名称

#---------------- 解决中文显示问题 -----------------------#


#---------------- 可视化全局设置 -----------------------#

# 设置全局字体大小
plt.rcParams['font.size'] = 12
# 设置图片清晰度
plt.rcParams['figure.dpi'] = 300

#---------------- 可视化全局设置 -----------------------#