import matplotlib
import matplotlib.pyplot as plt
import pandas as pd


# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 从Excel文件读取数据
def load_data_from_excel(file_path):
    """
    从Excel文件读取坐标和垃圾量数据
    """
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")

        id_col = '收集点编号'
        weight_col = 'w'

        # 数据类型转换
        df['x'] = pd.to_numeric(df['x'], errors='coerce')
        df['y'] = pd.to_numeric(df['y'], errors='coerce')
        df[weight_col] = pd.to_numeric(df[weight_col], errors='coerce')

        # 构建points字典
        points = {0: {'x': 0, 'y': 0, 'garbage1': 0}}

        for index, row in df.iterrows():
            point_id = int(row[id_col])
            points[point_id] = {
                'x': float(row['x']),
                'y': float(row['y']),
                'garbage1': float(row[weight_col])
            }

        return points
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return None

# 定义四个路线图的数据
route_configs = [
    {
        'title': '厨余垃圾 路径图',
        'excel_path': '../../resources/raw_data/simple 附件3_1.xlsx',
        'paths': [
            [0, 13, 4, 29, 31, 16, 0],
            [0, 22, 7, 14, 15, 5, 0],
            [0, 19, 28, 25, 9, 3, 0],
            [0, 26, 21, 30, 11, 27, 10, 0],
            [0, 23, 6, 24, 18, 8, 20, 17, 2, 0],
            [0, 12, 0],
        ],
        'description': '6 辆车，成本为 1099.63 元'
    },
    {
        'title': '可回收垃圾 路径图',
        'excel_path': '../../resources/raw_data/simple 附件3_2.xlsx',
        'paths': [
            [0, 12, 3, 19, 28, 9, 25, 13, 29, 4, 20, 31, 8, 18, 24, 6, 11, 27, 23, 10, 17, 16, 0],
            [0, 2, 22, 7, 14, 21, 30, 15, 5, 26, 0],
        ],
        'description': '2 辆车，成本为 453.53 元'
    },
    {
        'title': '有害垃圾 路径图',
        'excel_path': '../../resources/raw_data/simple 附件3_3.xlsx',
        'paths': [
            [0, 12, 3, 19, 28, 9, 25, 13, 31, 8, 18, 24, 6, 20, 29, 4, 17, 10, 23, 27, 11, 30, 15, 21, 14, 5, 26, 7, 22, 2, 16, 0]
        ],
        'description': '1 辆车，成本为 945.62 元'
    },
    {
        'title': '其他垃圾 路径图',
        'excel_path': '../../resources/raw_data/simple 附件3_4.xlsx',
        'paths': [
            # 车辆1: 0 → 16 → 2 → 5 → 14 → 21 → 30 → 15 → 27 → 20 → 10 → 26 → 7 → 22 → 0 (载重9.74吨，距离104.61km)
            [0, 16, 2, 5, 14, 21, 30, 15, 27, 20, 10, 26, 7, 22, 0],
            # 车辆2: 0 → 28 → 25 → 13 → 29 → 4 → 31 → 8 → 18 → 24 → 6 → 11 → 23 → 17 → 9 → 19 → 0 (载重9.97吨，距离139.08km)
            [0, 28, 25, 13, 29, 4, 31, 8, 18, 24, 6, 11, 23, 17, 9, 19, 0],
            # 车辆3: 0 → 3 → 12 → 0 (载重1.61吨，距离32.25km)
            [0, 3, 12, 0]
        ],
        'description': '3 辆车，成本为 496.67 元'
    }
]

def plot_single_route(ax, points, paths, title, description):
    """
    在指定的子图上绘制单个路线图
    """
    # 设置子图范围
    ax.set_xlim(-2, 42)
    ax.set_ylim(-2, 38)

    # 绘制处理厂（红色方形）
    ax.scatter(points[0]['x'], points[0]['y'], color='red', s=120, marker='s',
              edgecolors='black', linewidth=1, zorder=10, label='垃圾处理厂')
    ax.text(points[0]['x']+1, points[0]['y'], '0', fontsize=8, ha='left', va='center',
           fontweight='bold')

    # 绘制收集点（黑色圆点）
    max_point_id = max([k for k in points.keys() if k != 0])
    for idx in range(1, max_point_id + 1):
        if idx in points:
            x = points[idx]['x']
            y = points[idx]['y']
            ax.scatter(x, y, color='black', s=40, zorder=8)
            ax.text(x+0.8, y, f'{idx}', fontsize=7, ha='left', va='center')

    # 生成不同的颜色用于路径
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b',
              '#e377c2', '#7f7f7f', '#bcbd22', '#17becf', '#aec7e8', '#ffbb78',
              '#98df8a', '#ff9896', '#c5b0d5', '#c49c94', '#f7b6d3', '#c7c7c7']

    # 绘制路径
    legend_elements = []
    for i, path in enumerate(paths):
        if isinstance(path[0], list):
            path = path[0]

        # 提取坐标
        x_coords = [points[node]['x'] for node in path if node in points]
        y_coords = [points[node]['y'] for node in path if node in points]

        if len(x_coords) > 1:  # 确保路径有效
            color = colors[i % len(colors)]
            # 绘制路径线
            line = ax.plot(x_coords, y_coords, color=color, linewidth=1.5,
                          alpha=0.8, zorder=5)[0]

            # 添加到图例
            legend_elements.append((line, f'路径{i+1}'))

    # 设置子图属性
    ax.set_title(title, fontsize=11, fontweight='bold', pad=10)
    ax.set_xlabel('X坐标', fontsize=9)
    ax.set_ylabel('Y坐标', fontsize=9)
    ax.grid(True, linestyle='--', alpha=0.4, linewidth=0.5)

    # 添加描述文本
    ax.text(0.02, 0.98, description, transform=ax.transAxes, fontsize=8,
           verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 创建图例
    if legend_elements:
        lines, labels = zip(*legend_elements)
        ax.legend(lines, labels, bbox_to_anchor=(1.02, 1), loc='upper left',
                 fontsize=7, frameon=True, fancybox=True, shadow=True)

# 创建2x2子图布局
fig, axes = plt.subplots(2, 2, figsize=(18, 14), dpi=300)

# 为每个子图绘制路线
for i, config in enumerate(route_configs):
    row = i // 2
    col = i % 2
    ax = axes[row, col]

    points = load_data_from_excel(config['excel_path'])
    print(points)

    # 绘制路线图
    plot_single_route(ax, points, config['paths'], config['title'], config['description'])

# 调整布局以适应图例
plt.tight_layout()
plt.subplots_adjust(top=0.92, right=0.85)

# 保存图片
plt.show()