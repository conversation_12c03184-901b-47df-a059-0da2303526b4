import matplotlib
import matplotlib.pyplot as plt
import pandas as pd

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 从Excel文件读取数据
def load_data_from_excel(file_path):
    """
    从Excel文件读取坐标和垃圾量数据

    参数:
    file_path: Excel文件路径

    返回:
    points: 包含所有点信息的字典
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"Excel文件读取成功！文件: {file_path}")
        print("数据预览:")
        print(df.head())
        print(f"数据形状: {df.shape}")

        # 检查必要的列是否存在
        required_columns = ['收集点编号', 'x', 'y', 'w']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"警告：缺少列 {missing_columns}")
            print(f"可用列: {list(df.columns)}")

        # 数据类型转换
        df['x'] = pd.to_numeric(df['x'], errors='coerce')
        df['y'] = pd.to_numeric(df['y'], errors='coerce')
        df['w'] = pd.to_numeric(df['w'], errors='coerce')

        # 检查是否有无效数据
        if df[['x', 'y', 'w']].isnull().any().any():
            print("警告：数据中存在无效值，将被忽略")
            df = df.dropna(subset=['x', 'y', 'w'])

        # 构建points字典
        points = {0: {'x': 0, 'y': 0, 'garbage1': 0}}

        # 添加垃圾处理厂（编号0，坐标原点，垃圾量为0）

        # 添加收集点数据
        for index, row in df.iterrows():
            point_id = int(row['收集点编号'])
            points[point_id] = {
                'x': float(row['x']),
                'y': float(row['y']),
                'garbage1': float(row['w'])
            }

        print(f"成功加载 {len(points)} 个点的数据（包括垃圾处理厂）")
        return points
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return None

# 定义四个路线图的数据配置
route_configs = [
    {
        'title': '厨余垃圾收集路径图',
        'excel_path': 'resources/raw_data/simple 附件3_1.xlsx',
        'paths': [
            [0, 13, 4, 29, 31, 16, 0],
            [0, 22, 7, 14, 15, 5, 0],
            [0, 19, 28, 25, 9, 3, 0],
            [0, 26, 21, 30, 11, 27, 10, 0],
            [0, 23, 6, 24, 18, 8, 20, 17, 2, 0],
            [0, 12, 0],
        ]
    },
    {
        'title': '可回收垃圾收集路径图',
        'excel_path': 'resources/raw_data/simple 附件3_2.xlsx',
        'paths': [
            [0, 12, 3, 19, 28, 9, 25, 13, 29, 4, 20, 31, 8, 18, 24, 6, 11, 27, 23, 10, 17, 16, 0],
            [0, 2, 22, 7, 14, 21, 30, 15, 5, 26, 0],
        ]
    },
    {
        'title': '有害垃圾收集路径图',
        'excel_path': 'resources/raw_data/simple 附件3_3.xlsx',
        'paths': [
            [0, 12, 3, 19, 28, 9, 25, 13, 31, 8, 18, 24, 6, 20, 29, 4, 17, 10, 23, 27, 11, 30, 15, 21, 14, 5, 26, 7, 22, 2, 16, 0]
        ]
    },
    {
        'title': '其他垃圾收集路径图',
        'excel_path': 'resources/raw_data/simple 附件3_4.xlsx',
        'paths': [
            [0, 16, 2, 5, 14, 21, 30, 15, 27, 20, 10, 26, 7, 22, 0],
            [0, 28, 25, 13, 29, 4, 31, 8, 18, 24, 6, 11, 23, 17, 9, 19, 0],
            [0, 3, 12, 0]
        ]
    }
]

def plot_single_route(ax, points, paths, title):
    """
    在指定的子图上绘制单个路线图，完全按照原有风格
    """
    # 设置子图范围，与原图保持一致
    ax.set_xlim(-5, 45)
    ax.set_ylim(-5, 40)

    # 绘制处理厂，与原图样式完全一致
    ax.scatter(points[0]['x'], points[0]['y'], color='red', s=100, label='处理厂', zorder=5)
    ax.text(points[0]['x']+0.5, points[0]['y'], '0', fontsize=10, ha='left')

    # 动态确定收集点的数量（排除垃圾处理厂）
    max_point_id = max([k for k in points.keys() if k != 0])

    for idx in range(1, max_point_id + 1):
        if idx in points:  # 确保点存在
            x = points[idx]['x']
            y = points[idx]['y']
            garbage = points[idx]['garbage1']
            ax.scatter(x, y, color='blue', s=50)
            ax.text(x+0.5, y, f'{idx}({garbage}t)', fontsize=8, ha='left', va='center')

    # 绘制路径，使用与原图相同的颜色方案
    colors = plt.cm.tab20.colors
    for i, path in enumerate(paths):
        # 检查路径格式
        if isinstance(path[0], list):
            path = path[0]  # 处理嵌套列表的情况

        # 应用编号转换逻辑（与原文件保持一致）
        converted_path = [node if node == 0 else node - 1 for node in path]

        # 提取坐标
        x_coords = [points[node]['x'] for node in converted_path if node in points]
        y_coords = [points[node]['y'] for node in converted_path if node in points]

        # 绘制路径，与原图样式完全一致
        ax.plot(x_coords, y_coords, marker='o', color=colors[i],
               linewidth=1.5, markersize=5, label=f'路径{i+1}')

    # 设置图表属性，与原图保持一致
    ax.set_xlabel('X轴', fontsize=12)
    ax.set_ylabel('Y轴', fontsize=12)
    ax.grid(True, linestyle='--', alpha=0.6)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', title='路径编号')
    ax.set_title(title, fontsize=12, fontweight='bold')

# 创建2x2子图布局
fig, axes = plt.subplots(2, 2, figsize=(20, 16), dpi=300)
fig.suptitle('问题二：四种垃圾收集路径可视化对比', fontsize=16, fontweight='bold')

# 为每个子图绘制路线
for i, config in enumerate(route_configs):
    row = i // 2
    col = i % 2
    ax = axes[row, col]

    # 尝试加载数据
    points = load_data_from_excel(config['excel_path'])
    if points is None:
        print(f"跳过 {config['title']}，无法加载数据")
        continue

    # 绘制路线图
    plot_single_route(ax, points, config['paths'], config['title'])

# 调整布局
plt.tight_layout()
plt.subplots_adjust(top=0.93)

# 保存图片
plt.savefig('问题二_四种垃圾收集路径对比.png', dpi=300, bbox_inches='tight')
plt.show()

print("四个路线图合并完成！")
print("图片已保存为: 问题二_四种垃圾收集路径对比.png")
