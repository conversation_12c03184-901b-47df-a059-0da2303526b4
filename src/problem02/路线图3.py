import matplotlib
import matplotlib.pyplot as plt
import pandas as pd

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 从Excel文件读取数据
def load_data_from_excel(file_path):
    """
    从Excel文件读取坐标和垃圾量数据

    参数:
    file_path: Excel文件路径

    返回:
    points: 包含所有点信息的字典
    """
    # 读取Excel文件
    df = pd.read_excel(file_path)
    print("Excel文件读取成功！")
    print("数据预览:")
    print(df.head())
    print(f"数据形状: {df.shape}")

    # 检查必要的列是否存在
    required_columns = ['收集点编号', 'x', 'y', 'w']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"警告：缺少列 {missing_columns}")
        print(f"可用列: {list(df.columns)}")

    # 数据类型转换
    df['x'] = pd.to_numeric(df['x'], errors='coerce')
    df['y'] = pd.to_numeric(df['y'], errors='coerce')
    df['w'] = pd.to_numeric(df['w'], errors='coerce')

    # 检查是否有无效数据
    if df[['x', 'y', 'w']].isnull().any().any():
        print("警告：数据中存在无效值，将被忽略")
        df = df.dropna(subset=['x', 'y', 'w'])

    # 构建points字典
    points = {0: {'x': 0, 'y': 0, 'garbage1': 0}}

    # 添加垃圾处理厂（编号0，坐标原点，垃圾量为0）

    # 添加收集点数据
    for index, row in df.iterrows():
        point_id = int(row['收集点编号'])
        points[point_id] = {
            'x': float(row['x']),
            'y': float(row['y']),
            'garbage1': float(row['w'])
        }

    print(f"成功加载 {len(points)} 个点的数据（包括垃圾处理厂）")
    return points

# 加载数据
excel_file_path = '../../resources/raw_data/simple 附件3_3.xlsx'
points = load_data_from_excel(excel_file_path)

# 路径数据（示例数据，实际使用时替换为你的数据）
paths = [
    [0, 12, 3, 19, 28, 9, 25, 13, 29, 4, 20, 31, 8, 18, 24, 6, 11, 27, 23, 10, 17, 16, 0],
    [0, 2, 22, 7, 14, 21, 30, 15, 5, 26, 0],
]
paths = [[i if i == 0 else i - 1 for i in path] for path in paths]


# 创建图形
plt.figure(figsize=(10, 8))
plt.subplots_adjust(left=0.25, right=0.85, top=0.9, bottom=0.20)
plt.xlim(-5, 45)
plt.ylim(-5, 40)

# 绘制处理厂和收集点
plt.scatter(points[0]['x'], points[0]['y'], color='red', s=100, label='处理厂', zorder=5)
plt.text(points[0]['x']+0.5, points[0]['y'], '0', fontsize=10, ha='left')

# 动态确定收集点的数量（排除垃圾处理厂）
max_point_id = max([k for k in points.keys() if k != 0])

for idx in range(1, max_point_id + 1):
    if idx in points:  # 确保点存在
        # noinspection PyUnresolvedReferences
        x = points[idx]['x']
        y = points[idx]['y']
        garbage = points[idx]['garbage1']  # 修复：使用正确的键名
        plt.scatter(x, y, color='blue', s=50)
        plt.text(x+0.5, y, f'{idx}({garbage}t)', fontsize=8, ha='left', va='center')

# 绘制路径
colors = plt.cm.tab20.colors
for i, path in enumerate(paths):
    # 检查路径格式
    if isinstance(path[0], list):
        path = path[0]  # 处理嵌套列表的情况

    # 提取坐标
    x_coords = [points[node]['x'] for node in path]
    y_coords = [points[node]['y'] for node in path]

    # 绘制路径
    plt.plot(x_coords, y_coords, marker='o', color=colors[i], linewidth=1.5, markersize=5, label=f'路径{i+1}')

# 设置图表属性
plt.xlabel('X轴', fontsize=12)
plt.ylabel('Y轴', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.6)
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', title='路径编号')
plt.tight_layout()
plt.show()
