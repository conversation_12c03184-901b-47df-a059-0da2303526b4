import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 数据
points = {
    0: {'x': 0, 'y': 0, 'garbage': 0},
    1: {'x': 12, 'y': 8, 'garbage': 1.2},
    2: {'x': 5, 'y': 15, 'garbage': 2.3},
    3: {'x': 20, 'y': 30, 'garbage': 1.8},
    4: {'x': 25, 'y': 10, 'garbage': 3.1},
    5: {'x': 35, 'y': 22, 'garbage': 2.7},
    6: {'x': 18, 'y': 5, 'garbage': 1.5},
    7: {'x': 30, 'y': 35, 'garbage': 2.9},
    8: {'x': 10, 'y': 25, 'garbage': 1.1},
    9: {'x': 22, 'y': 18, 'garbage': 2.4},
    10: {'x': 38, 'y': 15, 'garbage': 3},
    11: {'x': 5, 'y': 8, 'garbage': 1.7},
    12: {'x': 15, 'y': 32, 'garbage': 2.1},
    13: {'x': 28, 'y': 5, 'garbage': 3.2},
    14: {'x': 30, 'y': 12, 'garbage': 2.6},
    15: {'x': 10, 'y': 10, 'garbage': 1.9},
    16: {'x': 20, 'y': 20, 'garbage': 2.5},
    17: {'x': 35, 'y': 30, 'garbage': 3.3},
    18: {'x': 8, 'y': 22, 'garbage': 1.3},
    19: {'x': 25, 'y': 25, 'garbage': 2.8},
    20: {'x': 32, 'y': 8, 'garbage': 3.4},
    21: {'x': 15, 'y': 5, 'garbage': 1.6},
    22: {'x': 28, 'y': 20, 'garbage': 2.2},
    23: {'x': 38, 'y': 25, 'garbage': 3.5},
    24: {'x': 10, 'y': 30, 'garbage': 1.4},
    25: {'x': 20, 'y': 10, 'garbage': 2},
    26: {'x': 30, 'y': 18, 'garbage': 3.6},
    27: {'x': 5, 'y': 25, 'garbage': 1},
    28: {'x': 18, 'y': 30, 'garbage': 2.3},
    29: {'x': 35, 'y': 10, 'garbage': 3.7},
    30: {'x': 22, 'y': 35, 'garbage': 1.9}
}

# 路径数据（示例数据，实际使用时替换为你的数据）
paths = [
    [0, 13, 6, 0], 
    [0, 5, 25, 0], 
    [0, 19, 22, 0], 
    [0, 14, 3, 0], 
    [0, 9, 16, 0], 
    [0, 4, 0], 
    [0, 7, 12, 0], 
    [0, 18, 27, 24, 0], 
    [0, 29, 0], 
    [0, 23, 1, 0], 
    [0, 20, 21, 0], 
    [0, 17, 8, 0], 
    [0, 2, 11, 0], 
    [0, 10, 15, 0], 
    [0, 28, 30, 0], 
    [0, 26, 0]
]

# 创建图形
plt.figure(figsize=(10, 8))
plt.subplots_adjust(left=0.25, right=0.85, top=0.9, bottom=0.20)
plt.xlim(-5, 45)
plt.ylim(-5, 40)

# 绘制处理厂和收集点
plt.scatter(points[0]['x'], points[0]['y'], color='red', s=100, label='处理厂', zorder=5)
plt.text(points[0]['x']+0.5, points[0]['y'], '0', fontsize=10, ha='left')

for idx in range(1, 31):
    x = points[idx]['x']
    y = points[idx]['y']
    garbage = points[idx]['garbage']
    plt.scatter(x, y, color='blue', s=50)
    plt.text(x+0.5,y, f'{idx}({garbage}t)', fontsize=8, ha='left', va='center')

# 绘制路径
colors = plt.cm.tab20.colors
for i, path in enumerate(paths):
    # 检查路径格式
    if isinstance(path[0], list):
        path = path[0]  # 处理嵌套列表的情况
    
    # 提取坐标
    x_coords = [points[node]['x'] for node in path]
    y_coords = [points[node]['y'] for node in path]
    
    # 绘制路径
    plt.plot(x_coords, y_coords, marker='o', color=colors[i], linewidth=1.5, markersize=5, label=f'路径{i+1}')

# 设置图表属性
plt.xlabel('X轴', fontsize=12)
plt.ylabel('Y轴', fontsize=12)
#plt.title('垃圾收集路径优化')
plt.grid(True, linestyle='--', alpha=0.6)
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', title='路径编号')
plt.tight_layout()
plt.show()
