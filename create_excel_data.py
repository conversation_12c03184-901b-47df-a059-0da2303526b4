import pandas as pd

# 创建示例数据
data = {
    '编号': list(range(1, 31)),
    'x': [12, 5, 20, 25, 35, 18, 30, 10, 22, 38, 5, 15, 28, 30, 10, 20, 35, 8, 25, 32, 15, 28, 38, 10, 20, 30, 5, 18, 35, 22],
    'y': [8, 15, 30, 10, 22, 5, 35, 25, 18, 15, 8, 32, 5, 12, 10, 20, 30, 22, 25, 8, 5, 20, 25, 30, 10, 18, 25, 30, 10, 35],
    '垃圾量': [1.2, 2.3, 1.8, 3.1, 2.7, 1.5, 2.9, 1.1, 2.4, 3.0, 1.7, 2.1, 3.2, 2.6, 1.9, 2.5, 3.3, 1.3, 2.8, 3.4, 1.6, 2.2, 3.5, 1.4, 2.0, 3.6, 1.0, 2.3, 3.7, 1.9]
}

df = pd.DataFrame(data)
df.to_excel('resources/raw_data/simple_附件3_1.xlsx', index=False)
print("Excel文件已创建成功！")
print(df.head())
